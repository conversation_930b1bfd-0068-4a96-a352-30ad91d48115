#!/usr/bin/env python3
"""
Validate the accuracy of ds_report.py against actual data
"""

import pandas as pd
import numpy as np

def validate_report():
    # Load and process data exactly as in notebooks
    fear_greed = pd.read_csv('csv_files/fear_greed_index (1).csv')
    trading_data = pd.read_csv('csv_files/historical_data.csv')

    fear_greed['date'] = pd.to_datetime(fear_greed['date'])
    trading_data['date'] = pd.to_datetime(trading_data['Timestamp IST'].str.split(' ').str[0], format='%d-%m-%Y')

    # Replicate notebook calculations
    trading_metrics = trading_data.groupby(['date', 'Account']).agg({
        'Size USD': ['sum', 'count', 'mean'],
        'Closed PnL': ['sum', 'mean'],
        'Fee': 'sum',
        'Side': lambda x: (x == 'BUY').mean()
    }).round(4)

    trading_metrics.columns = ['volume', 'trades', 'avg_trade_size', 'total_pnl', 'avg_pnl', 'fees', 'buy_ratio']
    trading_metrics = trading_metrics.reset_index()

    daily_metrics = trading_metrics.groupby('date').agg({
        'volume': 'sum',
        'trades': 'sum', 
        'total_pnl': 'sum',
        'avg_pnl': 'mean',
        'buy_ratio': 'mean',
        'Account': 'nunique'
    }).rename(columns={'Account': 'active_traders'})

    merged_data = pd.merge(fear_greed, daily_metrics, on='date', how='inner')
    merged_data['efficiency_ratio'] = merged_data['total_pnl'] / (merged_data['volume'] + 1e-6)

    print("=== REPORT VALIDATION ===")
    
    # Check sentiment distribution
    sentiment_counts = merged_data['classification'].value_counts()
    total_days = len(merged_data)
    
    print("\nSentiment Distribution (Actual vs Report):")
    report_values = {
        'Extreme Fear': ('12%', 14),
        'Fear': ('28%', 91), 
        'Neutral': ('35%', 67),
        'Greed': ('20%', 193),
        'Extreme Greed': ('5%', 114)
    }
    
    for sentiment in sentiment_counts.index:
        actual_count = sentiment_counts[sentiment]
        actual_pct = actual_count / total_days * 100
        report_pct, expected_count = report_values.get(sentiment, ('N/A', 0))
        print(f"{sentiment}: Actual {actual_pct:.1f}% ({actual_count} days) vs Report {report_pct}")
    
    # Check average values by sentiment
    print("\nAverage Daily Metrics by Sentiment:")
    behavior_analysis = merged_data.groupby('classification').agg({
        'volume': 'mean',
        'total_pnl': 'mean',
        'efficiency_ratio': 'mean'
    }).round(2)
    
    report_metrics = {
        'Extreme Fear': {'volume': 2.1e6, 'pnl': 1247, 'efficiency': 0.0059},
        'Fear': {'volume': 1.8e6, 'pnl': 892, 'efficiency': 0.0049},
        'Neutral': {'volume': 1.6e6, 'pnl': 743, 'efficiency': 0.0046},
        'Greed': {'volume': 1.9e6, 'pnl': 1156, 'efficiency': 0.0061},
        'Extreme Greed': {'volume': 2.3e6, 'pnl': 1089, 'efficiency': 0.0047}
    }
    
    for sentiment in behavior_analysis.index:
        actual = behavior_analysis.loc[sentiment]
        report = report_metrics.get(sentiment, {})
        
        print(f"\n{sentiment}:")
        print(f"  Volume: Actual ${actual['volume']:,.0f} vs Report ${report.get('volume', 0):,.0f}")
        print(f"  PnL: Actual ${actual['total_pnl']:,.0f} vs Report ${report.get('pnl', 0):,.0f}")
        print(f"  Efficiency: Actual {actual['efficiency_ratio']:.4f} vs Report {report.get('efficiency', 0):.4f}")
    
    # Check overall statistics
    print(f"\n=== OVERALL STATISTICS ===")
    print(f"Total analysis days: {len(merged_data)} (Report claims 479)")
    print(f"Total trading records: {len(trading_data)} (Report claims 211,226)")
    print(f"Date range: {merged_data['date'].min().date()} to {merged_data['date'].max().date()}")
    
    return merged_data

if __name__ == "__main__":
    data = validate_report()
