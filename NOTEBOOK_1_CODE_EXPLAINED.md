# Notebook 1: Code Explanation for Beginners

## Section 1: Import Libraries and Setup

### What are Libraries?
Libraries are pre-written code that other people created to make our work easier. Instead of writing everything from scratch, we use these tools.

```python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')
```

**What each library does:**
- **pandas (pd)**: Handles data like Excel spreadsheets
- **numpy (np)**: Does math calculations very fast
- **matplotlib.pyplot (plt)**: Creates basic charts and graphs
- **seaborn (sns)**: Makes prettier statistical charts
- **datetime**: Works with dates and times
- **plotly**: Creates interactive charts you can click and zoom
- **warnings**: Hides warning messages that might confuse us

### Setting Up Visual Styles

```python
plt.style.use('dark_background')
sns.set_palette("husl")

COLORS = {
    'fear': '#ff4757',           # Red color for fear
    'greed': '#2ed573',          # Green color for greed  
    'neutral': '#ffa502',        # Orange for neutral
    'extreme_fear': '#8b0000',   # Dark red for extreme fear
    'extreme_greed': '#006400',  # Dark green for extreme greed
    'accent': '#3742fa'          # Blue accent color
}
```

**What this does:**
- Makes charts have dark backgrounds (looks professional)
- Sets up colors that represent different emotions
- Creates a dictionary (like a phone book) to store color codes

## Section 2: Loading and Cleaning Data

### Reading CSV Files

```python
fear_greed = pd.read_csv('fear_greed_index (1).csv')
trading_data = pd.read_csv('historical_data.csv')
```

**What this does:**
- Loads data from CSV files (like Excel files but simpler)
- Creates two "DataFrames" (think of them as digital spreadsheets)

### Converting Text Dates to Real Dates

```python
fear_greed['date'] = pd.to_datetime(fear_greed['date'])
trading_data['date'] = pd.to_datetime(trading_data['Timestamp IST'].str.split(' ').str[0], format='%d-%m-%Y')
```

**Breaking this down:**
1. `pd.to_datetime()` - Converts text that looks like dates into actual date objects
2. `.str.split(' ')` - Splits text at spaces (separates date from time)
3. `.str[0]` - Takes only the first part (the date, not the time)
4. `format='%d-%m-%Y'` - Tells Python the date format (day-month-year)

### Extracting Hour Information

```python
trading_data['hour'] = pd.to_datetime(trading_data['Timestamp IST'], format='%d-%m-%Y %H:%S').dt.hour
```

**What this does:**
- Converts the full timestamp to datetime
- Extracts just the hour (0-23) from each trade
- This helps us see what times of day people trade most

### Creating Sentiment Scores

```python
sentiment_map = {
    'Extreme Fear': 0, 'Fear': 1, 'Neutral': 2, 'Greed': 3, 'Extreme Greed': 4
}
fear_greed['sentiment_score'] = fear_greed['classification'].map(sentiment_map)
```

**Why we do this:**
- Computers work better with numbers than words
- We convert "Extreme Fear" to 0, "Fear" to 1, etc.
- This lets us do math with sentiment levels

## Section 3: Aggregating Trading Data

### Grouping Data by Account and Date

```python
trading_metrics = trading_data.groupby(['date', 'Account']).agg({
    'Size USD': ['sum', 'count', 'mean'],
    'Closed PnL': ['sum', 'mean'],
    'Fee': 'sum',
    'Side': lambda x: (x == 'BUY').mean()
}).round(4)
```

**What `.groupby()` does:**
- Groups all trades by the same date and same account
- Like sorting your emails by sender and date

**What `.agg()` does:**
- Calculates different statistics for each group:
  - `'sum'` - Adds up all values
  - `'count'` - Counts how many trades
  - `'mean'` - Calculates average
  - `lambda x: (x == 'BUY').mean()` - Calculates percentage of buy orders

**The lambda function explained:**
- `lambda` creates a mini-function
- `(x == 'BUY')` creates True/False for each trade
- `.mean()` on True/False gives us the percentage (True=1, False=0)

### Renaming Columns for Clarity

```python
trading_metrics.columns = ['volume', 'trades', 'avg_trade_size', 'total_pnl', 'avg_pnl', 'fees', 'buy_ratio']
trading_metrics = trading_metrics.reset_index()
```

**What this does:**
- Gives our columns simple, clear names
- `reset_index()` makes date and Account regular columns instead of index

### Creating Daily Summaries

```python
daily_metrics = trading_metrics.groupby('date').agg({
    'volume': 'sum',           # Total volume per day
    'trades': 'sum',           # Total trades per day
    'total_pnl': 'sum',        # Total profit/loss per day
    'avg_pnl': 'mean',         # Average profit per trader
    'buy_ratio': 'mean',       # Average buy ratio across all traders
    'Account': 'nunique'       # Number of unique traders
}).rename(columns={'Account': 'active_traders'})
```

**What `'nunique'` does:**
- Counts unique values (how many different traders were active)

## Section 4: Merging Data and Creating New Metrics

### Combining Sentiment and Trading Data

```python
merged_data = pd.merge(fear_greed, daily_metrics, on='date', how='inner')
```

**What this does:**
- Combines the two datasets on matching dates
- `how='inner'` means only keep dates that exist in both datasets
- Like matching puzzle pieces that fit together

### Creating Efficiency Metrics

```python
merged_data['pnl_per_volume'] = merged_data['total_pnl'] / merged_data['volume']
merged_data['efficiency_ratio'] = merged_data['total_pnl'] / (merged_data['volume'] + 1e-6)
```

**What these metrics mean:**
- `pnl_per_volume`: How much profit per dollar traded
- `efficiency_ratio`: Similar, but adds tiny number (1e-6) to avoid division by zero
- `1e-6` means 0.000001 (scientific notation)

### Printing Summary Information

```python
print(f"Analysis period: {merged_data['date'].min()} to {merged_data['date'].max()}")
print(f"Total trading days: {len(merged_data)}")
print(f"Sentiment distribution:\n{merged_data['classification'].value_counts()}")
```

**What f-strings do:**
- `f"text {variable}"` inserts variable values into text
- `.min()` and `.max()` find earliest and latest dates
- `len()` counts how many rows of data we have
- `.value_counts()` counts how many times each sentiment appears

## Section 5: Creating Interactive Visualizations

### Setting Up Subplots

```python
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=('Fear & Greed Evolution', 'Sentiment Distribution', 
                   'Volume vs Sentiment', 'PnL Efficiency by Sentiment'),
    specs=[[{"secondary_y": True}, {"type": "pie"}],
           [{"secondary_y": True}, {"secondary_y": True}]]
)
```

**What this creates:**
- A 2x2 grid of charts (4 charts total)
- Each chart has a title
- Some charts can have two y-axes (left and right sides)
- One chart is a pie chart

### Creating Color Mapping

```python
color_map = {'Extreme Fear': COLORS['extreme_fear'], 'Fear': COLORS['fear'], 
             'Neutral': COLORS['neutral'], 'Greed': COLORS['greed'], 
             'Extreme Greed': COLORS['extreme_greed']}
```

**What this does:**
- Maps each sentiment to its color
- Makes all our charts use consistent colors

### Adding Line Chart (Fear & Greed Over Time)

```python
fig.add_trace(
    go.Scatter(x=merged_data['date'], y=merged_data['value'],
               mode='lines', name='F&G Index', line=dict(width=2, color=COLORS['accent'])),
    row=1, col=1
)
```

**What this does:**
- `go.Scatter()` creates a line chart (even though it's called "scatter")
- `x=merged_data['date']` puts dates on x-axis (horizontal)
- `y=merged_data['value']` puts fear/greed values on y-axis (vertical)
- `mode='lines'` makes it a line instead of dots
- `row=1, col=1` puts it in the top-left chart

### Adding Pie Chart (Sentiment Distribution)

```python
sentiment_counts = merged_data['classification'].value_counts()
fig.add_trace(
    go.Pie(labels=sentiment_counts.index, values=sentiment_counts.values,
           marker_colors=[color_map[x] for x in sentiment_counts.index],
           hole=0.4, name="Sentiment"),
    row=1, col=2
)
```

**Breaking this down:**
- `sentiment_counts.index` gets the sentiment names (Fear, Greed, etc.)
- `sentiment_counts.values` gets how many times each appears
- `[color_map[x] for x in sentiment_counts.index]` is a list comprehension that gets the right color for each sentiment
- `hole=0.4` makes it a donut chart (40% hole in middle)

### Adding Scatter Plot (Volume vs PnL by Sentiment)

```python
for sentiment in merged_data['classification'].unique():
    subset = merged_data[merged_data['classification'] == sentiment]
    fig.add_trace(
        go.Scatter(x=subset['volume'], y=subset['total_pnl'],
                   mode='markers', name=sentiment,
                   marker=dict(color=color_map[sentiment], size=8, opacity=0.7)),
        row=2, col=1
    )
```

**What this loop does:**
- Goes through each unique sentiment (Fear, Greed, etc.)
- Creates a subset of data for just that sentiment
- Adds dots to the chart where x=volume, y=profit/loss
- Each sentiment gets its own color
- `opacity=0.7` makes dots slightly transparent

### Adding Bar Chart (Efficiency by Sentiment)

```python
efficiency_by_sentiment = merged_data.groupby('classification')['efficiency_ratio'].mean().sort_values(ascending=False)
fig.add_trace(
    go.Bar(x=efficiency_by_sentiment.index, y=efficiency_by_sentiment.values,
           marker_color=[color_map[x] for x in efficiency_by_sentiment.index],
           name='Efficiency'),
    row=2, col=2
)
```

**What this does:**
- Groups data by sentiment and calculates average efficiency
- `.sort_values(ascending=False)` sorts from highest to lowest
- Creates a bar chart showing which sentiment is most efficient

## Section 6: Analyzing Trading Behavior Patterns

### Creating Behavior Analysis

```python
behavior_analysis = merged_data.groupby('classification').agg({
    'volume': ['mean', 'std'],
    'total_pnl': ['mean', 'std'],
    'buy_ratio': 'mean',
    'active_traders': 'mean',
    'trades': 'mean'
}).round(4)
```

**What `.agg()` with multiple functions does:**
- For volume and total_pnl: calculates both mean (average) and std (standard deviation)
- Standard deviation measures how spread out the data is
- For other columns: just calculates the mean

### Renaming Columns for Clarity

```python
behavior_analysis.columns = ['avg_volume', 'vol_volatility', 'avg_pnl', 'pnl_volatility',
                           'buy_preference', 'avg_traders', 'avg_trades']
behavior_analysis = behavior_analysis.reset_index()
```

**Why we rename:**
- Makes column names easier to understand
- 'vol_volatility' means how much volume varies
- 'buy_preference' is the average buy ratio

### Creating Multiple Bar Charts

```python
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Trading Behavior Across Market Sentiments', fontsize=20, y=0.98)
```

**What this creates:**
- A 2x3 grid of charts (6 charts total)
- `figsize=(18, 12)` makes it 18 inches wide, 12 inches tall
- `suptitle` adds a main title above all charts

### Looping Through Metrics

```python
metrics = ['avg_volume', 'avg_pnl', 'buy_preference', 'avg_traders', 'avg_trades', 'vol_volatility']
titles = ['Average Volume', 'Average PnL', 'Buy Preference', 'Active Traders', 'Trade Count', 'Volume Volatility']

for i, (metric, title) in enumerate(zip(metrics, titles)):
    ax = axes[i//3, i%3]
    colors = [color_map[idx] for idx in behavior_analysis.index]
    bars = ax.bar(behavior_analysis.index, behavior_analysis[metric], color=colors, alpha=0.8)
```

**Breaking down the loop:**
- `enumerate()` gives us both the position (i) and the values
- `zip()` pairs up metrics with their titles
- `i//3` gives us the row (0 or 1)
- `i%3` gives us the column (0, 1, or 2)
- `alpha=0.8` makes bars slightly transparent

### Adding Value Labels on Bars

```python
for bar, value in zip(bars, behavior_analysis[metric]):
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
            f'{value:.2f}', ha='center', va='bottom', fontsize=10)
```

**What this does:**
- Gets the height of each bar
- Calculates the center position of each bar
- Places text slightly above each bar showing the exact value
- `ha='center'` centers the text horizontally
- `va='bottom'` aligns text from the bottom
