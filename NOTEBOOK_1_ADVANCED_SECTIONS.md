# Notebook 1: Advanced Sections Explained

## Section 7: Correlation Analysis

### What is Correlation?
Correlation measures how two things move together. Values range from -1 to +1:
- **+1**: Perfect positive correlation (when one goes up, other goes up)
- **0**: No correlation (they move independently)  
- **-1**: Perfect negative correlation (when one goes up, other goes down)

### Creating Correlation Matrix

```python
correlation_features = ['value', 'volume', 'total_pnl', 'buy_ratio', 'active_traders', 'trades', 'efficiency_ratio']
corr_matrix = merged_data[correlation_features].corr()
```

**What this does:**
- Selects the columns we want to analyze
- `.corr()` calculates correlation between every pair of columns
- Creates a matrix (grid) showing all correlations

### Creating Heatmap

```python
mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdYlBu_r', center=0,
            square=True, linewidths=0.5, cbar_kws={"shrink": .8}, ax=ax1)
```

**Breaking this down:**
- `np.triu()` creates a triangular mask (hides duplicate correlations)
- `annot=True` shows correlation numbers on the heatmap
- `cmap='RdYlBu_r'` sets color scheme (Red-Yellow-Blue reversed)
- `center=0` makes 0 correlation the middle color
- `square=True` makes cells square-shaped

### Rolling Correlation

```python
rolling_corr = merged_data.set_index('date')[['value', 'total_pnl']].rolling(30).corr().iloc[1::2, 1]
```

**What rolling correlation does:**
- `set_index('date')` makes date the index for time-series analysis
- `rolling(30)` creates a 30-day moving window
- `.corr()` calculates correlation within each window
- `iloc[1::2, 1]` extracts just the correlation values we want

**Why use rolling correlation:**
- Shows how the relationship changes over time
- 30-day window smooths out daily noise
- Helps identify when relationships strengthen or weaken

## Section 8: Trading Strategy Development

### Creating Strategy Signals

```python
strategy_analysis = merged_data.copy()
strategy_analysis['sentiment_shift'] = strategy_analysis['sentiment_score'].diff()
strategy_analysis['volume_ma'] = strategy_analysis['volume'].rolling(7).mean()
strategy_analysis['pnl_ma'] = strategy_analysis['total_pnl'].rolling(7).mean()
```

**What each line does:**
- `.copy()` creates a duplicate to avoid changing original data
- `.diff()` calculates change from previous day
- `.rolling(7).mean()` creates 7-day moving average

**Why moving averages:**
- Smooth out daily fluctuations
- Show underlying trends
- Help identify when current values are above/below normal

### Defining Trading Signals

```python
contrarian_signals = (
    (strategy_analysis['classification'] == 'Extreme Fear') & 
    (strategy_analysis['buy_ratio'] < 0.4)
)
momentum_signals = (
    (strategy_analysis['classification'] == 'Extreme Greed') & 
    (strategy_analysis['buy_ratio'] > 0.6)
)
```

**What these conditions mean:**

**Contrarian Signals (Buy when others are scared):**
- Market sentiment is "Extreme Fear" AND
- Buy ratio is less than 40% (more people selling than buying)
- Logic: When everyone is scared and selling, prices might be too low

**Momentum Signals (Sell when others are greedy):**
- Market sentiment is "Extreme Greed" AND  
- Buy ratio is greater than 60% (more people buying than selling)
- Logic: When everyone is greedy and buying, prices might be too high

### Applying Signals

```python
strategy_analysis['signal'] = 'Hold'
strategy_analysis.loc[contrarian_signals, 'signal'] = 'Contrarian Buy'
strategy_analysis.loc[momentum_signals, 'signal'] = 'Momentum Sell'
```

**What `.loc[]` does:**
- Selects rows where the condition is True
- Changes the 'signal' column for those rows
- Default signal is 'Hold' (do nothing)

## Section 9: Strategy Performance Analysis

### Calculating Performance Metrics

```python
signal_performance = strategy_analysis.groupby('signal').agg({
    'total_pnl': ['mean', 'sum', 'count'],
    'volume': 'mean',
    'efficiency_ratio': 'mean'
}).round(4)
```

**What we're measuring:**
- **mean**: Average profit per day for each signal
- **sum**: Total profit from all days with that signal
- **count**: How many days had that signal
- **volume mean**: Average trading volume for each signal

### Creating Multi-Panel Dashboard

```python
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=('Strategy Performance', 'Signal Distribution', 
                   'Efficiency Comparison', 'Volume Analysis'),
    specs=[[{"type": "bar"}, {"type": "pie"}],
           [{"type": "bar"}, {"type": "bar"}]]
)
```

**Dashboard components:**
1. **Bar chart**: Shows average PnL for each signal
2. **Pie chart**: Shows how often each signal occurs
3. **Bar chart**: Shows efficiency (profit per dollar traded)
4. **Bar chart**: Shows average volume for each signal

## Section 10: Temporal (Time-Based) Analysis

### Hourly Trading Patterns

```python
hourly_patterns = trading_data.groupby(['hour', 'Side']).agg({
    'Size USD': 'sum',
    'Closed PnL': 'mean'
}).reset_index()
```

**What this reveals:**
- Which hours of the day have most trading activity
- Whether buy or sell orders dominate at different times
- Average profitability by hour and trade direction

### Pivot Tables for Visualization

```python
pivot_volume = hourly_patterns.pivot(index='hour', columns='Side', values='Size USD').fillna(0)
pivot_pnl = hourly_patterns.pivot(index='hour', columns='Side', values='Closed PnL').fillna(0)
```

**What pivot does:**
- Reshapes data from long format to wide format
- Creates separate columns for BUY and SELL
- Makes it easy to compare buy vs sell patterns
- `.fillna(0)` replaces missing values with 0

### Weekly Analysis

```python
weekly_sentiment = merged_data.copy()
weekly_sentiment['weekday'] = weekly_sentiment['date'].dt.day_name()
weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
weekly_sentiment['weekday'] = pd.Categorical(weekly_sentiment['weekday'], categories=weekday_order, ordered=True)
```

**Why we need ordered categories:**
- By default, pandas sorts alphabetically (Friday, Monday, Saturday...)
- We want chronological order (Monday, Tuesday, Wednesday...)
- `pd.Categorical()` with `ordered=True` fixes this

### Twin Axes Charts

```python
ax4_twin = ax4.twinx()
bars1 = ax4.bar(weekday_analysis.index, weekday_analysis['total_pnl'], 
                color=COLORS['greed'], alpha=0.7, label='PnL')
line1 = ax4_twin.plot(weekday_analysis.index, weekday_analysis['volume'], 
                     color=COLORS['fear'], marker='o', linewidth=2, label='Volume')
```

**What twin axes do:**
- Allow two different scales on same chart
- Left y-axis shows PnL values
- Right y-axis shows volume values
- Useful when comparing variables with very different ranges

## Section 11: Key Insights and Recommendations

### Finding Best Performers

```python
extreme_fear_performance = merged_data[merged_data['classification'] == 'Extreme Fear']['total_pnl'].mean()
extreme_greed_performance = merged_data[merged_data['classification'] == 'Extreme Greed']['total_pnl'].mean()
```

**What this calculates:**
- Average daily PnL during extreme fear periods
- Average daily PnL during extreme greed periods
- Helps determine which emotional state is more profitable

### Statistical Analysis

```python
sentiment_volatility = merged_data.groupby('classification')['total_pnl'].std()
volume_efficiency = merged_data.groupby('classification')['efficiency_ratio'].mean()
```

**What these metrics show:**
- **Volatility**: How unpredictable profits are for each sentiment
- **Efficiency**: How much profit per dollar traded for each sentiment
- Higher volatility = more risk
- Higher efficiency = better profit per dollar

### Creating Insights Dictionary

```python
insights = {
    'Contrarian Opportunity': f"Extreme Fear periods show {extreme_fear_performance:.2f} avg PnL",
    'Momentum Risk': f"Extreme Greed periods show {extreme_greed_performance:.2f} avg PnL", 
    'Volatility Leader': f"{sentiment_volatility.idxmax()} has highest PnL volatility ({sentiment_volatility.max():.2f})",
    'Efficiency Champion': f"{volume_efficiency.idxmax()} shows best volume efficiency ({volume_efficiency.max():.4f})"
}
```

**What this creates:**
- A dictionary of key findings
- Uses f-strings to format numbers nicely
- `.idxmax()` finds the index (sentiment) with the highest value
- `.max()` gets the actual highest value

This completes the explanation of Notebook 1. The code systematically analyzes market sentiment data, creates visualizations, develops trading strategies, and provides actionable insights for traders.
