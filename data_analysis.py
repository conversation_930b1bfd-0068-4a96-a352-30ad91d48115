#!/usr/bin/env python3
"""
Data analysis script to validate the implementation
"""

import pandas as pd
import numpy as np

def analyze_data():
    # Load data
    fear_greed = pd.read_csv('csv_files/fear_greed_index (1).csv')
    trading_data = pd.read_csv('csv_files/historical_data.csv')

    # Convert dates properly
    fear_greed['date'] = pd.to_datetime(fear_greed['date'])
    trading_data['date'] = pd.to_datetime(trading_data['Timestamp IST'].str.split(' ').str[0], format='%d-%m-%Y')

    print('=== FEAR & GREED DATA ===')
    print(f'Total records: {len(fear_greed)}')
    print(f'Date range: {fear_greed["date"].min()} to {fear_greed["date"].max()}')
    print(f'Unique classifications: {fear_greed["classification"].unique()}')
    print('Classification counts:')
    print(fear_greed['classification'].value_counts())

    print('\n=== TRADING DATA ===')
    print(f'Total records: {len(trading_data)}')
    print(f'Date range: {trading_data["date"].min()} to {trading_data["date"].max()}')
    print(f'Unique dates: {trading_data["date"].nunique()}')
    print(f'Unique accounts: {trading_data["Account"].nunique()}')

    print('\n=== DATA OVERLAP ===')
    merged = pd.merge(fear_greed, trading_data.groupby('date').size().reset_index(name='trades'), on='date', how='inner')
    print(f'Overlapping days: {len(merged)}')
    print(f'Overlap date range: {merged["date"].min()} to {merged["date"].max()}')
    
    # Check data quality issues
    print('\n=== DATA QUALITY CHECKS ===')
    print(f'Fear & Greed missing values: {fear_greed.isnull().sum().sum()}')
    print(f'Trading data missing values: {trading_data.isnull().sum().sum()}')
    
    # Check for realistic values
    print(f'Trading volume range: ${trading_data["Size USD"].min():.2f} to ${trading_data["Size USD"].max():.2f}')
    print(f'PnL range: ${trading_data["Closed PnL"].min():.2f} to ${trading_data["Closed PnL"].max():.2f}')
    
    # Validate sentiment mapping
    sentiment_map = {'Extreme Fear': 0, 'Fear': 1, 'Neutral': 2, 'Greed': 3, 'Extreme Greed': 4}
    fear_greed['sentiment_score'] = fear_greed['classification'].map(sentiment_map)
    print(f'Sentiment mapping successful: {fear_greed["sentiment_score"].isnull().sum() == 0}')

    # Validate key calculations from notebooks
    print('\n=== CALCULATION VALIDATION ===')

    # Replicate notebook 1 calculations
    trading_metrics = trading_data.groupby(['date', 'Account']).agg({
        'Size USD': ['sum', 'count', 'mean'],
        'Closed PnL': ['sum', 'mean'],
        'Fee': 'sum',
        'Side': lambda x: (x == 'BUY').mean()
    }).round(4)

    trading_metrics.columns = ['volume', 'trades', 'avg_trade_size', 'total_pnl', 'avg_pnl', 'fees', 'buy_ratio']
    trading_metrics = trading_metrics.reset_index()

    daily_metrics = trading_metrics.groupby('date').agg({
        'volume': 'sum',
        'trades': 'sum',
        'total_pnl': 'sum',
        'avg_pnl': 'mean',
        'buy_ratio': 'mean',
        'Account': 'nunique'
    }).rename(columns={'Account': 'active_traders'})

    merged_analysis = pd.merge(fear_greed, daily_metrics, on='date', how='inner')
    merged_analysis['pnl_per_volume'] = merged_analysis['total_pnl'] / merged_analysis['volume']
    merged_analysis['efficiency_ratio'] = merged_analysis['total_pnl'] / (merged_analysis['volume'] + 1e-6)

    print(f'Merged analysis records: {len(merged_analysis)}')
    print(f'Average daily volume: ${merged_analysis["volume"].mean():,.2f}')
    print(f'Average daily PnL: ${merged_analysis["total_pnl"].mean():,.2f}')

    # Check sentiment distribution in merged data
    print('\nSentiment distribution in analysis period:')
    print(merged_analysis['classification'].value_counts())

    # Validate behavior analysis calculations
    behavior_analysis = merged_analysis.groupby('classification').agg({
        'volume': ['mean', 'std'],
        'total_pnl': ['mean', 'std'],
        'buy_ratio': 'mean',
        'active_traders': 'mean',
        'trades': 'mean'
    }).round(4)

    print('\nBehavior analysis sample:')
    print(behavior_analysis.head())

    return merged_analysis

if __name__ == "__main__":
    merged_data = analyze_data()
