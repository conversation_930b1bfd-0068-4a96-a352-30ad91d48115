../../Scripts/kaleido_get_chrome.exe,sha256=yhmr69MVsKCXtqqGS7MtWD16BZ2LJggoW6o6m7VI7ZQ,108414
../../Scripts/kaleido_mocker.exe,sha256=HIW9pxP3zqQf8NcTU6z7MniQ2TEcGpKZt-wg8Fn-NyI,108395
docs/__pycache__/examples_script.cpython-39.pyc,,
docs/examples_script.py,sha256=iZnKDJsRpqGiBaGv8uCms20JLA8-trTpMQELvwzYnP4,2416
kaleido-1.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
kaleido-1.0.0.dist-info/LICENSE.md,sha256=I9mxX414BrISlVqBlBEGIFHkBp_kiSyuZpMcgy_IveM,1073
kaleido-1.0.0.dist-info/METADATA,sha256=eeXE2sEzLOa8RhZTdLhHySBS-kS8ZcZO7fsgN3Z2Aow,5601
kaleido-1.0.0.dist-info/RECORD,,
kaleido-1.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
kaleido-1.0.0.dist-info/WHEEL,sha256=iAkIy5fosb7FzIOwONchHf19Qu7_1wCWyFNR5gu9nU0,91
kaleido-1.0.0.dist-info/entry_points.txt,sha256=lIvqwcMqKvabtUcIdXD3mmGNtRNkibOil-_WX5j7O_M,128
kaleido-1.0.0.dist-info/top_level.txt,sha256=n74G4SoGguK1iZ2JGIpW2S3YiM3ed78J9dwXC769CyY,24
kaleido/__init__.py,sha256=O3_yMe7sUCoXyuy-jFIe9uGP2SMMztwVgg33YiLIh7I,3807
kaleido/__pycache__/__init__.cpython-39.pyc,,
kaleido/__pycache__/_fig_tools.cpython-39.pyc,,
kaleido/__pycache__/_kaleido_tab.cpython-39.pyc,,
kaleido/__pycache__/_mocker.cpython-39.pyc,,
kaleido/__pycache__/_page_generator.cpython-39.pyc,,
kaleido/__pycache__/_utils.cpython-39.pyc,,
kaleido/__pycache__/errors.cpython-39.pyc,,
kaleido/__pycache__/kaleido.cpython-39.pyc,,
kaleido/_fig_tools.py,sha256=RttPEA_ErCytR-aJ0P4sAm09u408NOdB5j5n8geQq60,4571
kaleido/_kaleido_tab.py,sha256=EwkZ80QZ5D_7uao1-GEdRAPwdQxMhWw5U5egWYz00oc,13103
kaleido/_mocker.py,sha256=imKb4v5Xui7fziiWFug4L7lljFk7YGM3ff2ZX2dDUpk,8468
kaleido/_page_generator.py,sha256=p6j8mLeRzctOIkrQ728BYzz1w_Bko8HRoUGDjFJoljw,3886
kaleido/_utils.py,sha256=UtNTXyKcJEJkPBYusgrPOciFU1JFj21kCfFrkHWw9lg,2709
kaleido/errors.py,sha256=k5KbEtwKHg19nP4tDlv6xY_yMjVCavf9oO98NfeKQFQ,364
kaleido/kaleido.py,sha256=mO9J_u0Q0im2E6qgES3K8Pnjnl8a46nC8p83EPhWhGA,19214
kaleido/vendor/index.html,sha256=wMu9gGGwbdx8sy8olxI9XB-n5aXUJRl1g593_T3OSTo,567
kaleido/vendor/kaleido_scopes.js,sha256=0q36ogXbkNuA09bG4y22FfojZDl_Q9EHlZMInvnDvTI,90287
site/__pycache__/examples_script.cpython-39.pyc,,
site/examples_script.py,sha256=iZnKDJsRpqGiBaGv8uCms20JLA8-trTpMQELvwzYnP4,2416
tests/__pycache__/conftest.cpython-39.pyc,,
tests/__pycache__/test_calc_fig.cpython-39.pyc,,
tests/__pycache__/test_page_generator.cpython-39.pyc,,
tests/__pycache__/test_placeholder.cpython-39.pyc,,
tests/conftest.py,sha256=G5zcUBfjyPkdRLvxkoCyBR_0_mF31yYGicZ8A_emTIU,615
tests/test_calc_fig.py,sha256=1N5rdqCNwLGI9bh0XrfOJICqgXviWEK0AXcUX7vLvF0,1096
tests/test_page_generator.py,sha256=xqJEDc1s_co8dV6hPAC9jjCF2abYMPn7NYll7SF4UvI,5190
tests/test_placeholder.py,sha256=y60hl1PL2UqeiUUXJeBQ4vT4lzVIak5Y7eXBxehIau0,302
