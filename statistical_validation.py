#!/usr/bin/env python3
"""
Validate statistical methods and ML implementations
"""

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA

def validate_statistical_methods():
    # Load and process data
    fear_greed = pd.read_csv('csv_files/fear_greed_index (1).csv')
    trading_data = pd.read_csv('csv_files/historical_data.csv')

    fear_greed['date'] = pd.to_datetime(fear_greed['date'])
    trading_data['date'] = pd.to_datetime(trading_data['Timestamp IST'].str.split(' ').str[0], format='%d-%m-%Y')

    # Calculate daily risk metrics
    daily_risk = trading_data.groupby('date').agg({
        'Size USD': ['sum', 'std', 'count'],
        'Closed PnL': ['sum', 'std', 'skew'],
        'Account': 'nunique'
    })

    daily_risk.columns = ['total_volume', 'volume_volatility', 'trade_count', 
                         'total_pnl', 'pnl_volatility', 'pnl_skewness', 'unique_traders']
    daily_risk = daily_risk.reset_index()

    risk_data = pd.merge(fear_greed, daily_risk, on='date', how='inner')
    risk_data['sharpe_proxy'] = risk_data['total_pnl'] / (risk_data['pnl_volatility'] + 1e-6)
    risk_data['volume_concentration'] = risk_data['total_volume'] / risk_data['trade_count']
    risk_data['trader_efficiency'] = risk_data['total_pnl'] / risk_data['unique_traders']

    print("=== STATISTICAL METHODS VALIDATION ===")
    
    # 1. Validate VaR and CVaR calculations
    def calculate_var(returns, confidence=0.05):
        return np.percentile(returns, confidence * 100)
    
    def calculate_cvar(returns, confidence=0.05):
        var = calculate_var(returns, confidence)
        return returns[returns <= var].mean()
    
    # Test VaR calculation
    test_returns = risk_data['total_pnl'].dropna()
    var_95 = calculate_var(test_returns, 0.05)
    cvar_95 = calculate_cvar(test_returns, 0.05)
    
    print(f"\nRisk Metrics Validation:")
    print(f"VaR (95%): ${var_95:,.2f}")
    print(f"CVaR (95%): ${cvar_95:,.2f}")
    print(f"VaR calculation correct: {var_95 <= 0}")  # VaR should be negative
    print(f"CVaR worse than VaR: {cvar_95 <= var_95}")  # CVaR should be worse than VaR
    
    # 2. Validate K-means clustering
    features_for_clustering = ['value', 'total_volume', 'pnl_volatility', 'sharpe_proxy', 'volume_concentration']
    ml_features = risk_data[features_for_clustering].fillna(0)
    
    scaler = StandardScaler()
    scaled_features = scaler.fit_transform(ml_features)
    
    kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
    clusters = kmeans.fit_predict(scaled_features)
    
    print(f"\nK-Means Clustering Validation:")
    print(f"Number of clusters: {len(np.unique(clusters))}")
    print(f"Cluster distribution: {np.bincount(clusters)}")
    print(f"Inertia (within-cluster sum of squares): {kmeans.inertia_:.2f}")
    
    # 3. Validate PCA
    pca = PCA(n_components=2)
    pca_features = pca.fit_transform(scaled_features)
    
    print(f"\nPCA Validation:")
    print(f"Explained variance ratio: {pca.explained_variance_ratio_}")
    print(f"Total variance explained: {pca.explained_variance_ratio_.sum():.3f}")
    print(f"PCA components shape: {pca.components_.shape}")
    
    # Feature importance validation
    feature_importance = pd.Series(pca.components_[0], index=ml_features.columns).abs().sort_values(ascending=False)
    print(f"Most important feature: {feature_importance.index[0]}")
    print(f"Feature importance sum: {feature_importance.sum():.3f}")
    
    # 4. Validate correlation calculations
    correlation_features = ['value', 'total_volume', 'total_pnl', 'unique_traders']
    corr_matrix = risk_data[correlation_features].corr()
    
    print(f"\nCorrelation Analysis Validation:")
    print(f"Correlation matrix shape: {corr_matrix.shape}")
    print(f"Diagonal values (should be 1.0): {np.diag(corr_matrix)}")
    print(f"Matrix is symmetric: {np.allclose(corr_matrix, corr_matrix.T)}")
    
    # 5. Validate rolling correlation
    rolling_corr = risk_data.set_index('date')[['value', 'total_pnl']].rolling(30).corr().iloc[1::2, 1]
    print(f"Rolling correlation range: {rolling_corr.min():.3f} to {rolling_corr.max():.3f}")
    print(f"Rolling correlation valid range: {-1 <= rolling_corr.min() <= rolling_corr.max() <= 1}")
    
    # 6. Validate statistical distributions
    print(f"\nDistribution Analysis:")
    for sentiment in risk_data['classification'].unique():
        subset = risk_data[risk_data['classification'] == sentiment]['total_pnl']
        if len(subset) > 3:  # Need at least 3 points for meaningful stats
            skewness = stats.skew(subset)
            kurtosis = stats.kurtosis(subset)
            print(f"{sentiment}: Skewness={skewness:.3f}, Kurtosis={kurtosis:.3f}")
    
    return risk_data

if __name__ == "__main__":
    data = validate_statistical_methods()
