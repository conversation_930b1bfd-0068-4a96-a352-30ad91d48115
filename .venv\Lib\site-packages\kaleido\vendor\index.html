<!DOCTYPE html>
<html>
  <head>
    <title>Kaleido-fier</title>
    <script>
      window.PlotlyConfig = {MathJaxConfig: 'local'};
    </script>
    <script type="text/x-mathjax-config">
      MathJax.Hub.Config({ "SVG": {blacker: 0 }});
    </script>
    <script src="https://cdn.plot.ly/plotly-2.35.3.min.js" charset="utf-8"></script>
    <script src="https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/tex-svg.js"></script>
    <script src="./kaleido_scopes.js"></script>
  </head>
  <body style="{margin: 0; padding: 0;}"><img id="kaleido-image"><img></body>
</html>
